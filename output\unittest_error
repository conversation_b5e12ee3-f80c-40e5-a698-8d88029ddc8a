=================================== FAILURES ===================================
_______________ TestDeleter.test_cleanup_builds_with_code_builds _______________
self = <dice_elipy_scripts.tests.test_deleter.TestDeleter object at 0x7fc121bb2f50>
mock_thread_pool = <MagicMock name='ThreadPool' id='140467443130768'>
mock_get_branch_set_under_path = <MagicMock name='get_branch_set_under_path' id='140467440741136'>
    @patch("dice_elipy_scripts.deleter.SETTINGS", ConfigManager(path=config_path))
    @patch("dice_elipy_scripts.deleter.get_branch_set_under_path")
    @patch("dice_elipy_scripts.deleter.ThreadPool")
    def test_cleanup_builds_with_code_builds(
        self, mock_thread_pool, mock_get_branch_set_under_path
    ):
        def fake_get_branch_set(path):
            branch_set = set()
            if path.endswith("code"):
                branch_set = {"branch1", "branch2", "branch3", "branch4"}
    
            return branch_set
    
        mock_get_branch_set_under_path.side_effect = fake_get_branch_set
        dry_run = True
        use_onefs_api = False
        cleanup_builds(dry_run, use_onefs_api)
    
        assert mock_thread_pool.call_count == 1
        map_args = mock_thread_pool.return_value.map.mock_calls[0][1][1]
        expected = [
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1",
                6,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4",
                9,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2",
                7,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3",
                8,
                dry_run,
                use_onefs_api,
                56,
            ),
        ]
>       assert sorted(map_args) == sorted(expected)
E       AssertionError: assert [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2', 7, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3', 8, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4', 9, True, False, 56)] == [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2', 7, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3', 8, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4', 9, True, False, 56)]
E         At index 0 diff: ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, True, False, 56) != ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, True, False, 56)
E         Full diff:
E           [
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1',
E         ?                                                     ^^    ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1',
E         ?                                                     ^    ^
E             6,
E             True,
E             False,
E             56),
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2',
E         ?                                                     ^^    ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2',
E         ?                                                     ^    ^
E             7,
E             True,
E             False,
E             56),
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3',
E         ?                                                     ^^    ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3',
E         ?                                                     ^    ^
E             8,
E             True,
E             False,
E             56),
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4',
E         ?                                                     ^^    ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4',
E         ?                                                     ^    ^
E             9,
E             True,
E             False,
E             56),
E           ]
dice_elipy_scripts/tests/test_deleter.py:188: AssertionError
----------------------------- Captured stdout call -----------------------------
2025-07-07 09:21:10 elipy2 [INFO]: Selected categories are: {'code': [{'default': 5}, {'branch1': 6}, {'branch2': 7}, {'branch3': 8}, {'branch4': 9}], 'frosty\\casablanca': [{'default': 20}, {'branch1': 21}, {'branch2': 22}, {'branch3': 23}, {'branch4': 24}]}
2025-07-07 09:21:10 elipy2 [INFO]: retaining 6 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/code/branch1
2025-07-07 09:21:10 elipy2 [INFO]: retaining 9 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/code/branch4
2025-07-07 09:21:10 elipy2 [INFO]: retaining 7 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/code/branch2
2025-07-07 09:21:10 elipy2 [INFO]: retaining 8 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/code/branch3
________ TestDeleter.test_cleanup_builds_with_code_builds_use_onefs_api ________
self = <dice_elipy_scripts.tests.test_deleter.TestDeleter object at 0x7fc121bbd6d0>
mock_thread_pool = <MagicMock name='ThreadPool' id='140467434719184'>
mock_get_branch_set_under_path = <MagicMock name='get_branch_set_under_path' id='140467435951248'>
    @patch("dice_elipy_scripts.deleter.SETTINGS", ConfigManager(path=config_path))
    @patch("dice_elipy_scripts.deleter.get_branch_set_under_path")
    @patch("dice_elipy_scripts.deleter.ThreadPool")
    def test_cleanup_builds_with_code_builds_use_onefs_api(
        self, mock_thread_pool, mock_get_branch_set_under_path
    ):
        def fake_get_branch_set(path):
            branch_set = set()
            if path.endswith("code"):
                branch_set = {"branch1", "branch2", "branch3", "branch4"}
            return branch_set
    
        mock_get_branch_set_under_path.side_effect = fake_get_branch_set
        dry_run = False
        use_onefs_api = True
        cleanup_builds(dry_run, use_onefs_api)
    
        assert mock_thread_pool.call_count == 1
        map_args = mock_thread_pool.return_value.map.mock_calls[0][1][1]
        expected = [
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1",
                6,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4",
                9,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2",
                7,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3",
                8,
                dry_run,
                use_onefs_api,
                56,
            ),
        ]
>       assert sorted(map_args) == sorted(expected)
E       AssertionError: assert [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2', 7, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3', 8, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4', 9, False, True, 56)] == [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2', 7, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3', 8, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4', 9, False, True, 56)]
E         At index 0 diff: ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, False, True, 56) != ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, False, True, 56)
E         Full diff:
E           [
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1',
E         ?                                                     ^^    ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1',
E         ?                                                     ^    ^
E             6,
E             False,
E             True,
E             56),
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2',
E         ?                                                     ^^    ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2',
E         ?                                                     ^    ^
E             7,
E             False,
E             True,
E             56),
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3',
E         ?                                                     ^^    ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3',
E         ?                                                     ^    ^
E             8,
E             False,
E             True,
E             56),
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4',
E         ?                                                     ^^    ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4',
E         ?                                                     ^    ^
E             9,
E             False,
E             True,
E             56),
E           ]
dice_elipy_scripts/tests/test_deleter.py:240: AssertionError
----------------------------- Captured stdout call -----------------------------
2025-07-07 09:21:10 elipy2 [INFO]: Selected categories are: {'code': [{'default': 5}, {'branch1': 6}, {'branch2': 7}, {'branch3': 8}, {'branch4': 9}], 'frosty\\casablanca': [{'default': 20}, {'branch1': 21}, {'branch2': 22}, {'branch3': 23}, {'branch4': 24}]}
2025-07-07 09:21:10 elipy2 [INFO]: retaining 6 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/code/branch1
2025-07-07 09:21:10 elipy2 [INFO]: retaining 9 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/code/branch4
2025-07-07 09:21:10 elipy2 [INFO]: retaining 7 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/code/branch2
2025-07-07 09:21:10 elipy2 [INFO]: retaining 8 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/code/branch3
__________ TestDeleter.test_cleanup_builds_with_code_builds_includes ___________
self = <dice_elipy_scripts.tests.test_deleter.TestDeleter object at 0x7fc121cf2d90>
mock_thread_pool = <MagicMock name='ThreadPool' id='140467451347152'>
mock_get_branch_set_under_path = <MagicMock name='get_branch_set_under_path' id='140467450932304'>
    @patch("dice_elipy_scripts.deleter.SETTINGS", ConfigManager(path=config_path))
    @patch("dice_elipy_scripts.deleter.get_branch_set_under_path")
    @patch("dice_elipy_scripts.deleter.ThreadPool")
    def test_cleanup_builds_with_code_builds_includes(
        self, mock_thread_pool, mock_get_branch_set_under_path
    ):
        def fake_get_branch_set(path):
            branch_set = set()
            if path.endswith("code") or path.endswith("casablanca"):
                branch_set = {"branch1", "branch2", "branch3", "branch4"}
    
            return branch_set
    
        mock_get_branch_set_under_path.side_effect = fake_get_branch_set
        dry_run = True
        use_onefs_api = False
        cleanup_builds(dry_run, use_onefs_api, include=["code"])
    
        assert mock_thread_pool.call_count == 1
        map_args = mock_thread_pool.return_value.map.mock_calls[0][1][1]
        expected = [
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1",
                6,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4",
                9,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2",
                7,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3",
                8,
                dry_run,
                use_onefs_api,
                56,
            ),
        ]
>       assert sorted(map_args) == sorted(expected)
E       AssertionError: assert [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2', 7, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3', 8, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4', 9, True, False, 56)] == [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2', 7, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3', 8, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4', 9, True, False, 56)]
E         At index 0 diff: ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, True, False, 56) != ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, True, False, 56)
E         Full diff:
E           [
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1',
E         ?                                                     ^^    ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1',
E         ?                                                     ^    ^
E             6,
E             True,
E             False,
E             56),
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2',
E         ?                                                     ^^    ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2',
E         ?                                                     ^    ^
E             7,
E             True,
E             False,
E             56),
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3',
E         ?                                                     ^^    ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3',
E         ?                                                     ^    ^
E             8,
E             True,
E             False,
E             56),
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4',
E         ?                                                     ^^    ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4',
E         ?                                                     ^    ^
E             9,
E             True,
E             False,
E             56),
E           ]
dice_elipy_scripts/tests/test_deleter.py:293: AssertionError
----------------------------- Captured stdout call -----------------------------
2025-07-07 09:21:10 elipy2 [INFO]: Selected categories are: {'code': [{'default': 5}, {'branch1': 6}, {'branch2': 7}, {'branch3': 8}, {'branch4': 9}]}
2025-07-07 09:21:10 elipy2 [INFO]: retaining 6 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/code/branch1
2025-07-07 09:21:10 elipy2 [INFO]: retaining 9 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/code/branch4
2025-07-07 09:21:10 elipy2 [INFO]: retaining 7 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/code/branch2
2025-07-07 09:21:10 elipy2 [INFO]: retaining 8 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/code/branch3
__________ TestDeleter.test_cleanup_builds_with_code_builds_excludes ___________
self = <dice_elipy_scripts.tests.test_deleter.TestDeleter object at 0x7fc121cf2fd0>
mock_thread_pool = <MagicMock name='ThreadPool' id='140467434940496'>
mock_get_branch_set_under_path = <MagicMock name='get_branch_set_under_path' id='140467444010384'>
    @patch("dice_elipy_scripts.deleter.SETTINGS", ConfigManager(path=config_path))
    @patch("dice_elipy_scripts.deleter.get_branch_set_under_path")
    @patch("dice_elipy_scripts.deleter.ThreadPool")
    def test_cleanup_builds_with_code_builds_excludes(
        self, mock_thread_pool, mock_get_branch_set_under_path
    ):
        def fake_get_branch_set(path):
            branch_set = set()
            if path.endswith("code") or path.endswith("casablanca"):
                branch_set = {"branch1", "branch2", "branch3", "branch4"}
    
            return branch_set
    
        mock_get_branch_set_under_path.side_effect = fake_get_branch_set
        dry_run = True
        use_onefs_api = False
        cleanup_builds(dry_run, use_onefs_api, exclude=["code"])
    
        assert mock_thread_pool.call_count == 1
        map_args = mock_thread_pool.return_value.map.mock_calls[0][1][1]
        expected = [
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch1",
                21,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch4",
                24,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch2",
                22,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch3",
                23,
                dry_run,
                use_onefs_api,
                56,
            ),
        ]
>       assert sorted(map_args) == sorted(expected)
E       AssertionError: assert [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch1', 21, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch2', 22, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch3', 23, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch4', 24, True, False, 56)] == [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch1', 21, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch2', 22, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch3', 23, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch4', 24, True, False, 56)]
E         At index 0 diff: ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch1', 21, True, False, 56) != ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch1', 21, True, False, 56)
E         Full diff:
E           [
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch1',
E         ?                                                     ^^                  ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch1',
E         ?                                                     ^                  ^
E             21,
E             True,
E             False,
E             56),
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch2',
E         ?                                                     ^^                  ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch2',
E         ?                                                     ^                  ^
E             22,
E             True,
E             False,
E             56),
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch3',
E         ?                                                     ^^                  ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch3',
E         ?                                                     ^                  ^
E             23,
E             True,
E             False,
E             56),
E         -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch4',
E         ?                                                     ^^                  ^^
E         +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch4',
E         ?                                                     ^                  ^
E             24,
E             True,
E             False,
E             56),
E           ]
dice_elipy_scripts/tests/test_deleter.py:346: AssertionError
----------------------------- Captured stdout call -----------------------------
2025-07-07 09:21:10 elipy2 [INFO]: Selected categories are: {'frosty\\casablanca': [{'default': 20}, {'branch1': 21}, {'branch2': 22}, {'branch3': 23}, {'branch4': 24}]}
2025-07-07 09:21:10 elipy2 [INFO]: retaining 21 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/frosty\casablanca/branch1
2025-07-07 09:21:10 elipy2 [INFO]: retaining 24 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/frosty\casablanca/branch4
2025-07-07 09:21:10 elipy2 [INFO]: retaining 22 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/frosty\casablanca/branch2
2025-07-07 09:21:10 elipy2 [INFO]: retaining 23 at file:\\fake-filer.dice.ad.ea.com\builds\Casablanca/frosty\casablanca/branch3